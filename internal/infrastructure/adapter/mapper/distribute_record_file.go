package mapper

import (
	"gorm.io/gorm"
)

const (
	TableNameDistributeRecordFile = "distribute_record_files"
)

// DistributeRecordFile 对应 distribute_record_files 表
type DistributeRecordFile struct {
	ID       string `gorm:"type:varchar(64);primary_key"`
	RecordID string `gorm:"type:varchar(64);index;comment:'关联DistributeRecord的ID'"`
	FileID   string `gorm:"type:varchar(64);comment:'文件id'"`
	FileName string `gorm:"type:varchar(255);comment:'文件名'"`
	Number   string `gorm:"type:varchar(255);comment:'文件编号'"`
	Version  string `gorm:"type:varchar(255);comment:'版本'"`
}

func (DistributeRecordFile) TableName() string {
	return TableNameDistributeRecordFile
}

// DistributeRecordFileClient 是 distribute_record_files 表的数据访问客户端
type DistributeRecordFileClient struct {
	db *gorm.DB
}

// NewDistributeRecordFileClient 创建一个新的 DistributeRecordFileClient 实例
func NewDistributeRecordFileClient(db *DocvaultDB) *DistributeRecordFileClient {
	return &DistributeRecordFileClient{
		db: db.GetDB(),
	}
}
