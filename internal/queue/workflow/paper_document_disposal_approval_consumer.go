package workflow

import (
	"context"
	"encoding/json"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logc"
)

type PaperDocumentDisposalApprovalConsumer struct {
	svcCtx *svc.ServiceContext
}

func NewPaperDocumentDisposalApprovalConsumer(svcCtx *svc.ServiceContext) *PaperDocumentDisposalApprovalConsumer {
	return &PaperDocumentDisposalApprovalConsumer{
		svcCtx: svcCtx,
	}
}

func (h *PaperDocumentDisposalApprovalConsumer) Handle(ctx context.Context, message []byte) error {
	var msg WorkflowEventMessage
	err := json.Unmarshal(message, &msg)
	if err != nil {
		logc.Errorf(ctx, "解析Kafka消息失败: %v", err)
		return err
	}

	h.handleApproved(ctx, msg)
	return nil
}

func (h *PaperDocumentDisposalApprovalConsumer) Name() string {
	return "file_disposal"
}

func (h *PaperDocumentDisposalApprovalConsumer) handleApproved(ctx context.Context, msg WorkflowEventMessage) {
	// 构造用户信息
	userLoginInfo := utils.UserLoginInfo{
		UserId:         msg.SponsorID,
		TenantId:       msg.TenantID,
		OrganizationId: msg.OrganizationID,
		DeviceKind:     1,
		IsVirtualUser:  false,
	}
	ctx = userLoginInfo.SetContext(ctx)

	var disposalApprovalInfo DisposalApprovalInfo
	if err := json.Unmarshal([]byte(msg.FormContent), &disposalApprovalInfo); err != nil {
		logc.Errorf(ctx, "处理表单信息失败: %v", err)
		return
	}

	data := disposalApprovalInfo.Data

	// 如果审批被拒绝，状态改回3
	if msg.EventType == consts.WorkflowEventRejected {
		updateUserDisposalStatusReq := h.apiReqToRpcReq(data)
		_, err := docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).UpdateUserDisposalStatus(ctx, updateUserDisposalStatusReq)
		if err != nil {
			logc.Errorf(ctx, "预处理回收审批失败: %v", err)
		}
		return
	}

	// 如果审批通过，保存处置信息
	recycleApprovalInfo := h.reqToRpc(&data)
	recycleApprovalInfo.WorkflowId = msg.WorkflowID
	// 获取审核人和审批人
	approvalInfo, err := GetApproval(ctx, h.svcCtx, msg)
	if err != nil {
		logc.Errorf(ctx, "获取审批人信息失败: %v", err)
		return
	}
	recycleApprovalInfo.ApprovalInfo = approvalInfo
	_, err = docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).SaveDisposalApprovalInfo(ctx, recycleApprovalInfo)
	if err != nil {
		logc.Errorf(ctx, "处理回收审批失败: %v", err)
	}
}

func (h *PaperDocumentDisposalApprovalConsumer) apiReqToRpcReq(disposalApprovalInfo DisposalApprovalInfoData) *docvault.UpdateUserDisposalStatusReq {
	updateUserDisposalStatusReq := &docvault.UpdateUserDisposalStatusReq{}
	updateUserDisposalStatusReq.DistributeId = disposalApprovalInfo.DistributeID
	var recycles []*docvault.RecycleList
	for _, v := range disposalApprovalInfo.DisposalList {
		permissions := []*docvault.FilePermission{
			{
				FileForm:       2,
				FilePermission: 3,
				ReceivedBy:     v.Permissions.ReceivedBy,
			},
		}
		recycles = append(recycles, &docvault.RecycleList{
			InventoryId: v.InventoryId,
			Permissions: permissions,
		})
	}
	updateUserDisposalStatusReq.Recycles = recycles
	updateUserDisposalStatusReq.DisposalStatus = 3
	return updateUserDisposalStatusReq
}

func (h *PaperDocumentDisposalApprovalConsumer) reqToRpc(data *DisposalApprovalInfoData) *docvault.DisposalApprovalInfo {
	disposalApprovalInfo := &docvault.DisposalApprovalInfo{}

	// 基本字段转换
	disposalApprovalInfo.DistributeId = data.DistributeID
	disposalApprovalInfo.DisposalDate = data.DisposalDate
	disposalApprovalInfo.DisposalReason = data.DisposalReason
	disposalApprovalInfo.WorkflowId = data.WorkflowID

	// 转换 DisposalList 数组
	var disposalList []*docvault.DisposalList
	for _, item := range data.DisposalList {
		disposalItem := &docvault.DisposalList{
			InventoryId: item.InventoryId,
		}

		// 转换 Permissions 数组
		permissions := []*docvault.FilePermission{
			{
				FileForm:       2,
				FilePermission: 3,
				ReceivedBy:     item.Permissions.ReceivedBy,
			},
		}
		disposalItem.Permissions = permissions
		disposalList = append(disposalList, disposalItem)
	}
	disposalApprovalInfo.DisposalList = disposalList

	return disposalApprovalInfo
}

type DisposalApprovalInfo struct {
	Data DisposalApprovalInfoData `json:"data"`
}

type DisposalApprovalInfoData struct {
	DistributeID   string         `json:"distributeId"`   // 发放信息列表ID
	DisposalDate   int64          `json:"disposalDate"`   // 处置日期
	DisposalReason string         `json:"disposalReason"` // 处置方式
	DisposalList   []DisposalList `json:"disposalList"`   // 处置清单
	WorkflowID     string         `json:"workflowId"`     // 工作流ID
}

type DisposalList struct {
	InventoryId string     `json:"inventoryId"` // 清单列表ID
	Permissions Permission `json:"permissions"` // 文件权限
}
