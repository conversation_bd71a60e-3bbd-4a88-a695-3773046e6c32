package document_library

import (
	"context"
	"encoding/json"
	"github.com/zeromicro/go-zero/core/logc"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/utils"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type WorkflowPreCallbackDistributeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWorkflowPreCallbackDistributeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WorkflowPreCallbackDistributeLogic {
	return &WorkflowPreCallbackDistributeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WorkflowPreCallbackDistributeLogic) WorkflowPreCallbackDistribute(req *types.WorkflowInfoReq) (resp *types.WorkflowInfoResp, err error) {
	// 构造用户信息
	userLoginInfo := utils.UserLoginInfo{
		UserId:         req.SponsorID,
		TenantId:       req.TenantID,
		OrganizationId: req.OrganizationID,
		DeviceKind:     1,
		IsVirtualUser:  false,
	}
	l.ctx = userLoginInfo.SetContext(l.ctx)
	// 处理表单信息
	var distributeApplication DistributeApplication
	if err = json.Unmarshal([]byte(req.FormContent), &distributeApplication); err != nil {
		l.Logger.Errorf("处理表单信息失败: %v", err)
		return nil, err
	}

	data := distributeApplication.Data

	// 获取文件分类
	fileCategory, err := mapper.NewBusinessDictionaryNodeRelationClient(l.svcCtx.NebulaDB).GetBusinessDictionaryNodeRelationByNodeID(l.ctx, data.TypeDictNodeId)
	if err != nil {
		l.Logger.Errorf("获取文件分类失败: %v", err)
		return nil, err
	}

	// 构造发放记录
	documentDistributeReq := l.apiReqToRpcReq(&data, &fileCategory)
	documentDistributeReq.WorkflowId = req.WorkflowID

	_, err = docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).PreSaveDistributeRecord(l.ctx, documentDistributeReq)
	if err != nil {
		logc.Errorf(l.ctx, "保存文件发放记录失败: %v", err)
		return nil, err
	}

	return &types.WorkflowInfoResp{}, nil
}

func (l *WorkflowPreCallbackDistributeLogic) apiReqToRpcReq(distributeApplication *DistributeApplicationData, fileCategory *mapper.BusinessDictionaryNodeRelation) *docvault.DocumentDistributeReq {
	documentDistributeReq := &docvault.DocumentDistributeReq{
		Id:                 distributeApplication.ID,
		Applicant:          distributeApplication.Applicant,
		ApplyDate:          distributeApplication.ApplyDate,
		DistributeType:     int32(distributeApplication.DistributeType),
		FileType:           int32(distributeApplication.FileType),
		TypeDictNodeId:     distributeApplication.TypeDictNodeId,
		Reason:             distributeApplication.Reason,
		OtherReason:        distributeApplication.OtherReason,
		WishDistributeDate: distributeApplication.WishDistributeDate,
		FileCategory:       fileCategory.Names,
		DistributeList:     nil,
		SaveMethod:         0,
	}

	for k1, distribute := range distributeApplication.DistributeList {
		documentDistributeReq.DistributeList = append(documentDistributeReq.DistributeList, &docvault.DistributeList{
			FileId:      distribute.FileId,
			FileName:    distribute.FileName,
			Number:      distribute.Number,
			Version:     distribute.Version,
			Permissions: nil,
		})
		for k2, permission := range distribute.Permissions {
			documentDistributeReq.DistributeList[k1].Permissions = append(documentDistributeReq.DistributeList[k1].Permissions, &docvault.Permission{
				FileForm:       int32(permission.FileForm),
				FilePermission: int32(permission.FilePermission),
				Recipient:      permission.Receiver,
				ReceivedBy:     nil,
			})
			for _, received := range permission.ReceivedBy {
				documentDistributeReq.DistributeList[k1].Permissions[k2].ReceivedBy = append(documentDistributeReq.DistributeList[k1].Permissions[k2].ReceivedBy, &docvault.Recipient{
					UserId:   received.UserId,
					UserName: received.UserName,
				})
			}
		}
	}
	return documentDistributeReq
}

type DistributeApplication struct {
	Data DistributeApplicationData `json:"data"`
}

type DistributeApplicationData struct {
	ID                 string `json:"ID"`
	Applicant          string `json:"applicant"`
	ApplyDate          int64  `json:"applyDate"`
	DistributeType     int    `json:"distributeType"`
	FileType           int    `json:"fileType"`
	TypeDictNodeId     string `json:"typeDictNodeId"`
	Reason             string `json:"reason"`
	OtherReason        string `json:"otherReason"`
	WishDistributeDate int64  `json:"wishDistributeDate"`
	DistributeList     []struct {
		FileId      string `json:"fileId"`
		FileName    string `json:"fileName"`
		Number      string `json:"number"`
		Version     string `json:"version"`
		Permissions []struct {
			FileForm       int    `json:"fileForm"`
			FilePermission int    `json:"filePermission"`
			Receiver       string `json:"receiver"`
			ReceivedBy     []struct {
				UserId   string `json:"userId"`
				UserName string `json:"userName"`
			} `json:"receivedBy"`
		} `json:"permissions"`
	} `json:"distributeList"`
	Category string `json:"category"`
}
