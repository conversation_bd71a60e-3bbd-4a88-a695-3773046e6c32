package document_library

import (
	"context"
	"errors"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDistributeListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDistributeListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDistributeListLogic {
	return &GetDistributeListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDistributeListLogic) GetDistributeList(req *types.GetDistributeListReq) (resp *types.GetDistributeListResp, err error) {
	// 使用 Applicant 模糊搜索申请人ids，如果登录用户是普通文件用户，则只差当前用户的申请记录
	applicant, err := l.getApplicant(req.Applicant)
	if err != nil {
		return nil, err
	}

	// 获取发放列表
	list, err := docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).GetDistributeInfoList(l.ctx, &docvault.GetDistributeListReq{
		PageInfo: &docvault.PageInfo{
			Page:     int32(req.Page),
			PageSize: int32(req.PageSize),
			NoPage:   req.NoPage,
		},
		FileNumber:     req.FileNumber,
		FileName:       req.FileName,
		FileType:       int32(req.FileType),
		FileCategory:   req.FileCategory,
		DistributeType: int32(req.DistributeType),
		Status:         int32(req.Status),
		Applicant:      applicant,
	})
	if err != nil {
		l.Logger.Errorf("获取发放信息列表失败: %v", err)
		return nil, err
	}

	resp = l.dataTransition(list)
	resp.Total = list.Total

	return resp, nil
}

func (l *GetDistributeListLogic) getApplicant(userName string) ([]string, error) {
	var ids []string
	users, err := l.svcCtx.PhoenixClient.GetUserInfoByNickname(l.ctx, userName)
	if err != nil {
		l.Logger.Errorf("获取用户信息失败: %v", err)
		return nil, err
	}
	for _, user := range users {
		ids = append(ids, user.ID)
	}
	userLoginInfo := utils.GetCurrentLoginUser(l.ctx)
	// 判断是否子公司普通用户
	code, err := l.svcCtx.PhoenixClient.CheckUserHasRoleCode(l.ctx, userLoginInfo.UserId, "ZGSWJYH")
	if err != nil {
		l.Logger.Errorf("获取用户角色失败: %v", err)
		return nil, err
	}
	// 如果是，只查出自己的申请记录
	if code {
		ids = []string{userLoginInfo.UserId}
		return ids, nil
	}
	// 判断是否子公司普通用户
	code, err = l.svcCtx.PhoenixClient.CheckUserHasRoleCode(l.ctx, userLoginInfo.UserId, "JTWJYH")
	if err != nil {
		l.Logger.Errorf("获取用户角色失败: %v", err)
		return nil, err
	}
	// 如果是，只查出自己的申请记录
	if code {
		ids = []string{userLoginInfo.UserId}
		return ids, nil
	}
	// 判断是否集团文件管理员
	code, err = l.svcCtx.PhoenixClient.CheckUserHasRoleCode(l.ctx, userLoginInfo.UserId, "JTWJGLY")
	if err != nil {
		l.Logger.Errorf("获取用户角色失败: %v", err)
		return nil, err
	}
	// 如果是，根据搜索条件查
	if code {
		return ids, nil
	}
	// 判断是否子公司文件管理员
	code, err = l.svcCtx.PhoenixClient.CheckUserHasRoleCode(l.ctx, userLoginInfo.UserId, "ZGSWJGLY")
	if err != nil {
		l.Logger.Errorf("获取用户角色失败: %v", err)
		return nil, err
	}
	if code {
		return ids, nil
	}
	return nil, errors.New("当前用户不是文件用户")
}

func (l *GetDistributeListLogic) dataTransition(list *docvault.GetDistributeListResp) *types.GetDistributeListResp {
	var getDistributeListInfo []types.GetDistributeListInfo
	for _, v := range list.Data {
		// 已接收
		var received []types.DistributeUser
		for _, user := range v.Received {
			received = append(received, types.DistributeUser{
				UserID:   user.UserId,
				FileForm: user.FileForm,
				Nickname: user.Nickname,
			})
		}
		// 未接收
		var notReceived []types.DistributeUser
		for _, user := range v.NotReceived {
			notReceived = append(notReceived, types.DistributeUser{
				UserID:   user.UserId,
				FileForm: user.FileForm,
				Nickname: user.Nickname,
			})
		}
		// 已回收
		var recycle []types.DistributeUser
		for _, user := range v.Recycle {
			recycle = append(recycle, types.DistributeUser{
				UserID:   user.UserId,
				FileForm: user.FileForm,
				Nickname: user.Nickname,
			})
		}
		// 已处置
		var disposalBy []types.DistributeUser
		for _, user := range v.DisposalBy {
			disposalBy = append(disposalBy, types.DistributeUser{
				UserID:   user.UserId,
				FileForm: user.FileForm,
				Nickname: user.Nickname,
			})
		}
		approvalInfo := types.ApprovalInfo{}
		if v.ApprovalInfo != nil {
			for _, r := range v.ApprovalInfo.Approvers {
				approvalInfo.Approvers = append(approvalInfo.Approvers, types.Approval{
					UserID:       r.UserId,
					PassedDate:   r.PassedDate,
					UserNickname: r.Nickname,
				})
			}
			for _, r := range v.ApprovalInfo.Auditors {
				approvalInfo.Auditors = append(approvalInfo.Auditors, types.Approval{
					UserID:       r.UserId,
					PassedDate:   r.PassedDate,
					UserNickname: r.Nickname,
				})
			}
		}
		getDistributeListInfo = append(getDistributeListInfo, types.GetDistributeListInfo{
			ID:                 v.Id,
			Applicant:          v.Applicant,
			ApplyDate:          v.ApplyDate,
			DistributeType:     int(v.DistributeType),
			FileType:           int(v.FileType),
			FileCategory:       v.FileCategory,
			Reason:             v.Reason,
			OtherReason:        v.OtherReason,
			WishDistributeDate: v.WishDistributeDate,
			Status:             int(v.Status),
			WorkflowID:         v.WorkflowId,
			ApprovalInfo:       approvalInfo,
			DistributeCount:    int(v.DistributeCount),
			DisposalCount:      len(disposalBy),
			ReceivedCount:      len(received),
			Received:           received,
			NotReceived:        notReceived,
			Recycle:            recycle,
			DisposalBy:         disposalBy,
		})
	}
	return &types.GetDistributeListResp{
		Data: getDistributeListInfo,
	}
}
